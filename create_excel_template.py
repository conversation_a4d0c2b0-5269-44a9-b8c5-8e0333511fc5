#!/usr/bin/env python3
"""
Excel Template Creator for Code Review Summary
Creates a formatted Excel template with proper headers and styling
"""

import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
from datetime import datetime

def create_code_review_template():
    """Create a formatted Excel template for code review summaries"""
    
    # Create workbook and worksheet
    wb = Workbook()
    ws = wb.active
    ws.title = "Code Review Summary"
    
    # Define headers
    headers = [
        'Project Name',
        'Review Date', 
        'Source Type',
        'Source URL',
        'Coding Standards',
        'Logic',
        'Modularity',
        'Database Design',
        'Frontend Design',
        'Performance',
        'Scalability',
        'Security',
        'Usability',
        'Total Score',
        'Folder Link',
        'Review Status',
        'Reviewer Notes'
    ]
    
    # Add headers to first row
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        
        # Header styling
        cell.font = Font(bold=True, color="FFFFFF")
        cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        cell.alignment = Alignment(horizontal="center", vertical="center")
        cell.border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
    
    # Add sample data row
    sample_data = [
        'Sample Project',
        datetime.now().strftime('%Y-%m-%d'),
        'GitHub',
        'https://github.com/user/sample-project',
        8,  # Coding Standards
        7,  # Logic
        6,  # Modularity
        5,  # Database Design
        7,  # Frontend Design
        6,  # Performance
        5,  # Scalability
        6,  # Security
        7,  # Usability
        57, # Total Score
        'https://drive.google.com/drive/folders/sample-folder-id',
        'Completed',
        'Good foundation, needs security improvements'
    ]
    
    for col, value in enumerate(sample_data, 1):
        cell = ws.cell(row=2, column=col, value=value)
        cell.alignment = Alignment(horizontal="center", vertical="center")
        cell.border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # Color coding for scores
        if col >= 5 and col <= 13:  # Score columns
            if isinstance(value, int):
                if value >= 8:
                    cell.fill = PatternFill(start_color="C6EFCE", end_color="C6EFCE", fill_type="solid")  # Green
                elif value >= 6:
                    cell.fill = PatternFill(start_color="FFEB9C", end_color="FFEB9C", fill_type="solid")  # Yellow
                else:
                    cell.fill = PatternFill(start_color="FFC7CE", end_color="FFC7CE", fill_type="solid")  # Red
        
        # Total score color coding
        if col == 14:  # Total Score column
            if isinstance(value, int):
                if value >= 72:  # 80% of 90
                    cell.fill = PatternFill(start_color="C6EFCE", end_color="C6EFCE", fill_type="solid")  # Green
                elif value >= 54:  # 60% of 90
                    cell.fill = PatternFill(start_color="FFEB9C", end_color="FFEB9C", fill_type="solid")  # Yellow
                else:
                    cell.fill = PatternFill(start_color="FFC7CE", end_color="FFC7CE", fill_type="solid")  # Red
    
    # Adjust column widths
    column_widths = {
        'A': 20,  # Project Name
        'B': 12,  # Review Date
        'C': 12,  # Source Type
        'D': 30,  # Source URL
        'E': 15,  # Coding Standards
        'F': 10,  # Logic
        'G': 12,  # Modularity
        'H': 15,  # Database Design
        'I': 15,  # Frontend Design
        'J': 12,  # Performance
        'K': 12,  # Scalability
        'L': 10,  # Security
        'M': 12,  # Usability
        'N': 12,  # Total Score
        'O': 35,  # Folder Link
        'P': 15,  # Review Status
        'Q': 30   # Reviewer Notes
    }
    
    for col, width in column_widths.items():
        ws.column_dimensions[col].width = width
    
    # Freeze the header row
    ws.freeze_panes = 'A2'
    
    # Add a summary section
    summary_row = 4
    ws.cell(row=summary_row, column=1, value="Summary Statistics").font = Font(bold=True, size=14)
    
    # Add formulas for summary statistics
    ws.cell(row=summary_row + 1, column=1, value="Total Projects:")
    ws.cell(row=summary_row + 1, column=2, value="=COUNTA(A:A)-1")
    
    ws.cell(row=summary_row + 2, column=1, value="Average Score:")
    ws.cell(row=summary_row + 2, column=2, value="=AVERAGE(N:N)")
    
    ws.cell(row=summary_row + 3, column=1, value="Completed Reviews:")
    ws.cell(row=summary_row + 3, column=2, value='=COUNTIF(P:P,"Completed")')
    
    ws.cell(row=summary_row + 4, column=1, value="Pending Reviews:")
    ws.cell(row=summary_row + 4, column=2, value='=COUNTIF(P:P,"Pending")')
    
    # Add conditional formatting instructions
    instructions_row = summary_row + 6
    ws.cell(row=instructions_row, column=1, value="Color Coding:").font = Font(bold=True)
    ws.cell(row=instructions_row + 1, column=1, value="Green: Score 8-10 (Excellent)")
    ws.cell(row=instructions_row + 2, column=1, value="Yellow: Score 6-7 (Good)")
    ws.cell(row=instructions_row + 3, column=1, value="Red: Score 0-5 (Needs Improvement)")
    
    # Save the template
    wb.save('Code_Review_Summary_Template.xlsx')
    print("Excel template created: Code_Review_Summary_Template.xlsx")

def create_pandas_template():
    """Create a simple pandas-based template"""
    
    headers = [
        'Project Name', 'Review Date', 'Source Type', 'Source URL',
        'Coding Standards', 'Logic', 'Modularity', 'Database Design',
        'Frontend Design', 'Performance', 'Scalability', 'Security',
        'Usability', 'Total Score', 'Folder Link', 'Review Status'
    ]
    
    # Create empty DataFrame with headers
    df = pd.DataFrame(columns=headers)
    
    # Add sample row
    sample_row = {
        'Project Name': 'Sample Project',
        'Review Date': datetime.now().strftime('%Y-%m-%d'),
        'Source Type': 'GitHub',
        'Source URL': 'https://github.com/user/sample-project',
        'Coding Standards': 8,
        'Logic': 7,
        'Modularity': 6,
        'Database Design': 5,
        'Frontend Design': 7,
        'Performance': 6,
        'Scalability': 5,
        'Security': 6,
        'Usability': 7,
        'Total Score': 57,
        'Folder Link': 'https://drive.google.com/drive/folders/sample-folder-id',
        'Review Status': 'Completed'
    }
    
    df = pd.concat([df, pd.DataFrame([sample_row])], ignore_index=True)
    
    # Save as Excel
    df.to_excel('Code_Review_Summary_Simple.xlsx', index=False)
    print("Simple Excel template created: Code_Review_Summary_Simple.xlsx")

if __name__ == "__main__":
    print("Creating Excel templates for code review summary...")
    create_code_review_template()
    create_pandas_template()
    print("Templates created successfully!")
