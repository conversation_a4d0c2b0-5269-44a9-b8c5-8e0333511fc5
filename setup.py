#!/usr/bin/env python3
"""
Setup Script for Code Review Automation
Automates the initial setup process
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        return False
    print(f"✅ Python {sys.version.split()[0]} detected")
    return True

def install_dependencies():
    """Install required Python packages"""
    print("\n📦 Installing Python dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def create_directory_structure():
    """Create necessary directories"""
    print("\n📁 Creating directory structure...")
    
    directories = [
        "logs",
        "temp",
        "backups",
        "config"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")
    
    return True

def create_config_template():
    """Create configuration template"""
    print("\n⚙️  Creating configuration template...")
    
    config = {
        "google_drive": {
            "credentials_file": "credentials.json",
            "token_file": "token.json",
            "root_folder_name": "Code Reviews",
            "requests_folder_name": "Code Review Requests"
        },
        "openai": {
            "model": "gpt-4",
            "temperature": 0.3,
            "max_tokens": 2000
        },
        "excel": {
            "summary_file_name": "Code_Review_Summary.xlsx",
            "auto_backup": True,
            "backup_frequency": "daily"
        },
        "workflow": {
            "max_files_per_repo": 20,
            "supported_extensions": [".py", ".js", ".jsx", ".ts", ".tsx", ".java", ".cpp", ".c", ".cs", ".php", ".rb", ".go", ".rs"],
            "exclude_patterns": ["node_modules", "__pycache__", ".git", "venv", "env"]
        }
    }
    
    config_file = "config/workflow_config.json"
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"✅ Configuration template created: {config_file}")
    return True

def create_excel_template():
    """Create Excel template"""
    print("\n📊 Creating Excel template...")
    try:
        subprocess.check_call([sys.executable, "create_excel_template.py"])
        print("✅ Excel template created successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to create Excel template: {e}")
        return False

def check_credentials():
    """Check if Google Drive credentials exist"""
    print("\n🔐 Checking credentials...")
    
    if os.path.exists("credentials.json"):
        print("✅ Google Drive credentials found")
        return True
    else:
        print("⚠️  Google Drive credentials not found")
        print("   Please download credentials.json from Google Cloud Console")
        print("   See README.md for detailed instructions")
        return False

def validate_n8n_workflow():
    """Validate n8n workflow file"""
    print("\n🔄 Validating n8n workflow...")
    
    workflow_file = "n8n-code-review-workflow.json"
    if not os.path.exists(workflow_file):
        print(f"❌ Workflow file not found: {workflow_file}")
        return False
    
    try:
        with open(workflow_file, 'r') as f:
            workflow = json.load(f)
        
        if 'nodes' not in workflow or 'connections' not in workflow:
            print("❌ Invalid workflow format")
            return False
        
        print(f"✅ Workflow validated ({len(workflow['nodes'])} nodes)")
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in workflow file: {e}")
        return False

def create_sample_input_files():
    """Create sample input files for testing"""
    print("\n📝 Creating sample input files...")
    
    # GitHub URL file
    github_url_content = "https://github.com/octocat/Hello-World"
    with open("sample_github_url.txt", 'w') as f:
        f.write(github_url_content)
    print("✅ Created sample_github_url.txt")
    
    # Instructions file
    instructions = """
# Sample Input Files Created

## For GitHub Repository Review:
- Upload 'sample_github_url.txt' to your Google Drive 'Code Review Requests' folder

## For Direct Code Review:
- Create a ZIP file with your code
- Upload the ZIP file to your Google Drive 'Code Review Requests' folder

## For Testing:
- Use the sample_project/ folder as a test repository
- Create a ZIP of the sample_project/ folder and upload it

The n8n workflow will automatically detect new files and process them.
"""
    
    with open("SAMPLE_USAGE.md", 'w') as f:
        f.write(instructions)
    print("✅ Created SAMPLE_USAGE.md")
    
    return True

def run_setup():
    """Run the complete setup process"""
    print("🚀 Code Review Automation Setup")
    print("=" * 50)
    
    setup_steps = [
        ("Checking Python version", check_python_version),
        ("Installing dependencies", install_dependencies),
        ("Creating directories", create_directory_structure),
        ("Creating configuration", create_config_template),
        ("Creating Excel template", create_excel_template),
        ("Validating n8n workflow", validate_n8n_workflow),
        ("Creating sample files", create_sample_input_files),
        ("Checking credentials", check_credentials)
    ]
    
    results = []
    for step_name, step_function in setup_steps:
        print(f"\n🔄 {step_name}...")
        try:
            result = step_function()
            results.append(result)
        except Exception as e:
            print(f"❌ {step_name} failed: {e}")
            results.append(False)
    
    # Summary
    print("\n" + "=" * 50)
    print("🏁 Setup Summary")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ Steps Completed: {passed}/{total}")
    
    if passed == total:
        print("\n🎉 Setup completed successfully!")
        print("\n📋 Next Steps:")
        print("1. Set up Google Drive API credentials (if not done)")
        print("2. Import n8n-code-review-workflow.json into your n8n instance")
        print("3. Configure OpenAI and Google Drive credentials in n8n")
        print("4. Test the workflow with sample files")
        print("5. Run 'python test_workflow.py' to validate everything")
    else:
        print("\n⚠️  Setup incomplete. Please resolve the issues above.")
        print("   Check README.md for detailed instructions.")
    
    print(f"\n📚 Documentation: README.md")
    print(f"🧪 Testing: python test_workflow.py")
    print(f"⚙️  Configuration: config/workflow_config.json")

if __name__ == "__main__":
    run_setup()
