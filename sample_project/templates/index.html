{% extends "base.html" %}

{% block title %}Dashboard - Task Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>Welcome, {{ user.username }}!</h2>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTaskModal">
                <i class="fas fa-plus"></i> Add Task
            </button>
        </div>

        {% if tasks %}
            <div class="row">
                {% for task in tasks %}
                <div class="col-md-6 mb-3">
                    <div class="card task-card priority-{{ task.priority }} {% if task.completed %}completed-task{% endif %}">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start">
                                <h5 class="card-title">{{ task.title }}</h5>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a class="dropdown-item" href="{{ url_for('complete_task', task_id=task.id) }}">
                                                <i class="fas fa-{% if task.completed %}undo{% else %}check{% endif %}"></i>
                                                {% if task.completed %}Mark Incomplete{% else %}Mark Complete{% endif %}
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item text-danger" href="{{ url_for('delete_task', task_id=task.id) }}" 
                                               onclick="return confirm('Are you sure you want to delete this task?')">
                                                <i class="fas fa-trash"></i> Delete
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            
                            {% if task.description %}
                            <p class="card-text">{{ task.description }}</p>
                            {% endif %}
                            
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-flag"></i> {{ task.priority.title() }} Priority
                                </small>
                                {% if task.due_date %}
                                <small class="text-muted">
                                    <i class="fas fa-calendar"></i> Due: {{ task.due_date.strftime('%Y-%m-%d') }}
                                </small>
                                {% endif %}
                            </div>
                            
                            <small class="text-muted d-block mt-2">
                                Created: {{ task.created_at.strftime('%Y-%m-%d %H:%M') }}
                            </small>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">No tasks yet</h4>
                <p class="text-muted">Create your first task to get started!</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTaskModal">
                    <i class="fas fa-plus"></i> Add Your First Task
                </button>
            </div>
        {% endif %}
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-pie"></i> Task Statistics</h5>
            </div>
            <div class="card-body">
                {% set total_tasks = tasks|length %}
                {% set completed_tasks = tasks|selectattr('completed')|list|length %}
                {% set pending_tasks = total_tasks - completed_tasks %}
                
                <div class="row text-center">
                    <div class="col-4">
                        <h3 class="text-primary">{{ total_tasks }}</h3>
                        <small class="text-muted">Total</small>
                    </div>
                    <div class="col-4">
                        <h3 class="text-success">{{ completed_tasks }}</h3>
                        <small class="text-muted">Completed</small>
                    </div>
                    <div class="col-4">
                        <h3 class="text-warning">{{ pending_tasks }}</h3>
                        <small class="text-muted">Pending</small>
                    </div>
                </div>
                
                {% if total_tasks > 0 %}
                <div class="progress mt-3">
                    <div class="progress-bar" role="progressbar" 
                         style="width: {{ (completed_tasks / total_tasks * 100)|round }}%">
                        {{ (completed_tasks / total_tasks * 100)|round }}%
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Add Task Modal -->
<div class="modal fade" id="addTaskModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Task</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('add_task') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="title" class="form-label">Task Title *</label>
                        <input type="text" class="form-control" id="title" name="title" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <label for="priority" class="form-label">Priority</label>
                            <select class="form-select" id="priority" name="priority">
                                <option value="low">Low</option>
                                <option value="medium" selected>Medium</option>
                                <option value="high">High</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="due_date" class="form-label">Due Date</label>
                            <input type="date" class="form-control" id="due_date" name="due_date">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Task</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
