# Task Manager - Sample Python Project

A simple Flask-based task management web application for testing code review workflows.

## Features

- User registration and authentication
- Create, read, update, and delete tasks
- Task prioritization (High, Medium, Low)
- Due date tracking
- Task completion status
- Dashboard with statistics
- RESTful API endpoint for tasks
- Responsive Bootstrap UI

## Technology Stack

- **Backend**: Flask (Python web framework)
- **Database**: SQLite with SQLAlchemy ORM
- **Frontend**: HTML, CSS, JavaScript with Bootstrap 5
- **Authentication**: Flask sessions with password hashing
- **Icons**: Font Awesome

## Installation

1. Clone or download this project
2. Create a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Run the application:
   ```bash
   python app.py
   ```

5. Open your browser and go to `http://localhost:5000`

## Usage

1. Register a new account or login with existing credentials
2. Add tasks with titles, descriptions, priorities, and due dates
3. Mark tasks as complete/incomplete
4. Delete tasks when no longer needed
5. View task statistics on the dashboard
6. Access task data via API at `/api/tasks`

## Project Structure

```
sample_project/
├── app.py                 # Main Flask application
├── requirements.txt       # Python dependencies
├── README.md             # This file
└── templates/            # HTML templates
    ├── base.html         # Base template with navigation
    ├── index.html        # Dashboard/home page
    ├── login.html        # Login form
    └── register.html     # Registration form
```

## Database Schema

### Users Table
- id (Primary Key)
- username (Unique)
- email (Unique)
- password_hash
- created_at

### Tasks Table
- id (Primary Key)
- title
- description
- completed (Boolean)
- priority (String: high/medium/low)
- due_date
- created_at
- user_id (Foreign Key)

## API Endpoints

- `GET /` - Dashboard (requires authentication)
- `GET /login` - Login page
- `POST /login` - Process login
- `GET /register` - Registration page
- `POST /register` - Process registration
- `GET /logout` - Logout user
- `POST /add_task` - Create new task
- `GET /complete_task/<id>` - Toggle task completion
- `GET /delete_task/<id>` - Delete task
- `GET /api/tasks` - JSON API for user's tasks

## Security Features

- Password hashing using Werkzeug
- Session-based authentication
- CSRF protection through Flask's built-in features
- User authorization (users can only access their own tasks)

## Testing the Code Review

This project is designed to test various aspects of code review:

1. **Coding Standards**: Mixed quality - some good practices, some areas for improvement
2. **Logic**: Basic CRUD operations with proper error handling
3. **Modularity**: Simple Flask structure, could be more modular
4. **Database Design**: Basic relational design with foreign keys
5. **Frontend Design**: Bootstrap-based responsive UI
6. **Performance**: Basic implementation, no optimization
7. **Scalability**: Single-file app, not designed for scale
8. **Security**: Basic security measures implemented
9. **Usability**: Clean, intuitive interface

## Known Issues (Intentional for Testing)

- Secret key is hardcoded (should use environment variable)
- No input validation on frontend
- No rate limiting
- No logging implementation
- Database not optimized for production
- No unit tests included
- No error pages (404, 500)
- No email verification for registration

This project serves as a good baseline for testing automated code review systems.
