# Code Review Automation with n8n and Google Drive

An automated code review workflow using n8n, OpenAI API, and Google Drive integration. This system can analyze code repositories from GitHub URLs or uploaded files, generate comprehensive review reports, and organize results in Google Drive with Excel summaries.

## 🚀 Features

- **Multi-Input Support**: Accept GitHub URLs or direct file uploads via Google Drive
- **AI-Powered Reviews**: Uses OpenAI GPT-4 for intelligent code analysis
- **Comprehensive Scoring**: Evaluates 9 key aspects of code quality
- **Automated Organization**: Creates structured folders in Google Drive
- **Excel Reporting**: Maintains a consolidated summary of all reviews
- **Detailed Reports**: Generates markdown reports for each project

## 📋 Review Criteria

The system evaluates code on these 9 aspects (scored 1-10):

1. **Coding Standards** - Formatting, naming conventions, documentation
2. **Logic** - Algorithm efficiency, control flow, error handling
3. **Modularity** - Code organization, reusability, separation of concerns
4. **Database Design** - Schema design, relationships, indexing
5. **Frontend Design** - UI/UX, accessibility, responsive design
6. **Performance** - Optimization, caching, resource usage
7. **Scalability** - Architecture for growth, load handling
8. **Security** - Input validation, authentication, data protection
9. **Usability** - User experience, error messages, documentation

## 🛠 Prerequisites

### Required Accounts & APIs
- **n8n Instance** (self-hosted or cloud)
- **OpenAI API Key** (GPT-4 access recommended)
- **Google Account** with Drive API access
- **Google Cloud Project** for Drive API credentials

### Software Requirements
- Python 3.8+ (for helper scripts)
- Node.js 16+ (for n8n if self-hosting)

## 📦 Installation & Setup

### Step 1: Google Drive API Setup

1. **Create Google Cloud Project**
   ```bash
   # Go to Google Cloud Console
   https://console.cloud.google.com/
   ```

2. **Enable Google Drive API**
   - Navigate to "APIs & Services" > "Library"
   - Search for "Google Drive API"
   - Click "Enable"

3. **Create Service Account Credentials**
   ```bash
   # Go to "APIs & Services" > "Credentials"
   # Click "Create Credentials" > "Service Account"
   # Download the JSON key file as 'credentials.json'
   ```

4. **Set up OAuth2 Credentials** (for n8n)
   - Create OAuth 2.0 Client ID
   - Add authorized redirect URIs for your n8n instance
   - Download client configuration

### Step 2: n8n Setup

#### Option A: n8n Cloud
1. Sign up at [n8n.cloud](https://n8n.cloud)
2. Create a new workflow

#### Option B: Self-Hosted n8n
```bash
# Install n8n globally
npm install n8n -g

# Start n8n
n8n start

# Access at http://localhost:5678
```

### Step 3: Configure n8n Credentials

1. **OpenAI API Credentials**
   - Go to n8n Settings > Credentials
   - Add "OpenAI API" credential
   - Enter your OpenAI API key

2. **Google Drive OAuth2 Credentials**
   - Add "Google Drive OAuth2 API" credential
   - Use the OAuth2 client ID and secret from Step 1
   - Complete the authorization flow

### Step 4: Import Workflow

1. **Import the Workflow**
   ```bash
   # In n8n interface:
   # 1. Click "Import from File"
   # 2. Select 'n8n-code-review-workflow.json'
   # 3. Click "Import"
   ```

2. **Configure Node Credentials**
   - Assign OpenAI credentials to OpenAI nodes
   - Assign Google Drive credentials to Google Drive nodes

3. **Update Excel File ID**
   - Create or locate your summary Excel file in Google Drive
   - Copy the file ID from the URL
   - Update the "EXCEL_SUMMARY_FILE_ID" in the "Update Excel Summary" node

### Step 5: Python Helper Scripts Setup

```bash
# Install Python dependencies
pip install google-api-python-client google-auth-httplib2 google-auth-oauthlib pandas openpyxl requests

# Create Excel template
python create_excel_template.py

# Upload template to Google Drive and note the file ID
```

### Step 6: Google Drive Folder Structure

Create the following folder structure in your Google Drive:

```
📁 Code Reviews/
├── 📁 Code Review Requests/    # Input folder (monitored by n8n)
├── 📁 2024-01-15/             # Date-based folders (auto-created)
│   ├── 📁 Project-Name-1/     # Individual project folders
│   │   ├── 📄 detailed_review.md
│   │   └── 📄 project_files...
│   └── 📁 Project-Name-2/
└── 📄 Code_Review_Summary.xlsx # Master summary file
```

## 🎯 Usage

### Method 1: GitHub URL Review

1. **Create Input File**
   ```bash
   # Create a text file with GitHub URL
   echo "https://github.com/username/repository" > github_review_request.txt
   ```

2. **Upload to Google Drive**
   - Upload the text file to "Code Review Requests" folder
   - n8n will automatically detect and process it

### Method 2: Direct File Upload

1. **Prepare Code Files**
   - Create a ZIP file with your code
   - Or upload individual code files

2. **Upload to Google Drive**
   - Upload to "Code Review Requests" folder
   - n8n will process the files

### Expected Output

After processing, you'll find:

1. **Organized Folder Structure**
   ```
   📁 Code Reviews/2024-01-15/ProjectName/
   ├── 📄 ProjectName_detailed_review.md
   └── 📄 Additional files...
   ```

2. **Updated Excel Summary**
   - New row added with scores and links
   - Color-coded based on performance

3. **Detailed Markdown Report**
   - Comprehensive analysis
   - Scores for all 9 criteria
   - Strengths and weaknesses
   - Recommendations

## 🔧 Customization

### Modify Review Criteria

Edit the OpenAI prompt in the workflow nodes to:
- Add new evaluation criteria
- Adjust scoring weights
- Change output format
- Add specific technology focus

### Adjust Folder Structure

Modify the "Create Folder Structure" node to:
- Change naming conventions
- Add additional subfolders
- Modify organization logic

### Excel Template Customization

```python
# Edit create_excel_template.py to:
# - Add new columns
# - Change color coding
# - Modify formulas
# - Add charts/graphs
```

## 🧪 Testing

### Test with Sample Project

```bash
# Use the included sample project
cd sample_project/

# Create a ZIP file
zip -r sample_project.zip .

# Upload to Google Drive "Code Review Requests" folder
# Or create a text file with a GitHub URL
```

### Verify Workflow

1. Check n8n execution logs
2. Verify folder creation in Google Drive
3. Check Excel file updates
4. Review generated markdown reports

## 🐛 Troubleshooting

### Common Issues

1. **Google Drive API Quota Exceeded**
   ```bash
   # Solution: Implement rate limiting in workflow
   # Add "Wait" nodes between API calls
   ```

2. **OpenAI API Rate Limits**
   ```bash
   # Solution: Add retry logic and delays
   # Use GPT-3.5-turbo for faster processing
   ```

3. **File Processing Errors**
   ```bash
   # Check file formats and sizes
   # Ensure proper permissions on Google Drive
   ```

4. **Workflow Not Triggering**
   ```bash
   # Verify Google Drive trigger configuration
   # Check folder permissions and sharing settings
   ```

### Debug Mode

Enable debug mode in n8n:
```bash
# Set environment variable
export N8N_LOG_LEVEL=debug

# Restart n8n
n8n start
```

## 📊 Performance Optimization

### For High Volume Processing

1. **Batch Processing**
   - Process multiple files in single execution
   - Implement queuing system

2. **Caching**
   - Cache frequently accessed data
   - Store intermediate results

3. **Parallel Processing**
   - Use n8n's parallel execution
   - Split large repositories

## 🔒 Security Considerations

1. **API Key Management**
   - Store keys securely in n8n credentials
   - Rotate keys regularly
   - Use environment variables

2. **Google Drive Permissions**
   - Limit folder access
   - Use service accounts for automation
   - Regular permission audits

3. **Data Privacy**
   - Review data retention policies
   - Implement data encryption
   - Consider GDPR compliance

## 📈 Monitoring & Analytics

### Track Workflow Performance

1. **n8n Execution Data**
   - Monitor success/failure rates
   - Track execution times
   - Identify bottlenecks

2. **Excel Analytics**
   - Add pivot tables for insights
   - Create charts for trends
   - Generate periodic reports

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Make improvements
4. Test thoroughly
5. Submit pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For issues and questions:
1. Check the troubleshooting section
2. Review n8n documentation
3. Check Google Drive API limits
4. Verify OpenAI API status

## 🔄 Updates & Maintenance

### Regular Maintenance Tasks

1. **Update Dependencies**
   ```bash
   # Update n8n
   npm update n8n -g
   
   # Update Python packages
   pip install --upgrade -r requirements.txt
   ```

2. **Monitor API Usage**
   - Check OpenAI usage and billing
   - Monitor Google Drive API quotas
   - Review n8n execution limits

3. **Backup Configuration**
   - Export n8n workflows regularly
   - Backup Google Drive folder structure
   - Save Excel templates and data

---

**Happy Code Reviewing! 🎉**
