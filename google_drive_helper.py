#!/usr/bin/env python3
"""
Google Drive Helper for Code Review Automation
Handles file operations, folder creation, and Excel report management
"""

import os
import json
import pandas as pd
from datetime import datetime
from typing import Dict, List, Optional
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload, MediaIoBaseDownload
from google.oauth2.credentials import Credentials
from google.auth.transport.requests import Request
from google_auth_oauthlib.flow import InstalledAppFlow
import io

class GoogleDriveManager:
    """Manages Google Drive operations for code review workflow"""
    
    SCOPES = ['https://www.googleapis.com/auth/drive']
    
    def __init__(self, credentials_file: str = 'credentials.json', token_file: str = 'token.json'):
        self.credentials_file = credentials_file
        self.token_file = token_file
        self.service = None
        self._authenticate()
    
    def _authenticate(self):
        """Authenticate with Google Drive API"""
        creds = None
        
        # Load existing token
        if os.path.exists(self.token_file):
            creds = Credentials.from_authorized_user_file(self.token_file, self.SCOPES)
        
        # If no valid credentials, get new ones
        if not creds or not creds.valid:
            if creds and creds.expired and creds.refresh_token:
                creds.refresh(Request())
            else:
                flow = InstalledAppFlow.from_client_secrets_file(
                    self.credentials_file, self.SCOPES)
                creds = flow.run_local_server(port=0)
            
            # Save credentials for next run
            with open(self.token_file, 'w') as token:
                token.write(creds.to_json())
        
        self.service = build('drive', 'v3', credentials=creds)
    
    def create_folder(self, name: str, parent_id: Optional[str] = None) -> str:
        """Create a folder in Google Drive"""
        folder_metadata = {
            'name': name,
            'mimeType': 'application/vnd.google-apps.folder'
        }
        
        if parent_id:
            folder_metadata['parents'] = [parent_id]
        
        folder = self.service.files().create(body=folder_metadata, fields='id').execute()
        return folder.get('id')
    
    def find_or_create_folder(self, name: str, parent_id: Optional[str] = None) -> str:
        """Find existing folder or create new one"""
        query = f"name='{name}' and mimeType='application/vnd.google-apps.folder'"
        if parent_id:
            query += f" and '{parent_id}' in parents"
        
        results = self.service.files().list(q=query, fields="files(id, name)").execute()
        folders = results.get('files', [])
        
        if folders:
            return folders[0]['id']
        else:
            return self.create_folder(name, parent_id)
    
    def upload_file(self, file_path: str, name: str, parent_id: Optional[str] = None, 
                   mime_type: Optional[str] = None) -> str:
        """Upload a file to Google Drive"""
        file_metadata = {'name': name}
        if parent_id:
            file_metadata['parents'] = [parent_id]
        
        if mime_type:
            media = MediaFileUpload(file_path, mimetype=mime_type)
        else:
            media = MediaFileUpload(file_path)
        
        file = self.service.files().create(
            body=file_metadata, media_body=media, fields='id'
        ).execute()
        
        return file.get('id')
    
    def upload_text_content(self, content: str, name: str, parent_id: Optional[str] = None) -> str:
        """Upload text content as a file to Google Drive"""
        file_metadata = {'name': name}
        if parent_id:
            file_metadata['parents'] = [parent_id]
        
        # Create temporary file
        temp_file = f"/tmp/{name}"
        with open(temp_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        try:
            file_id = self.upload_file(temp_file, name, parent_id, 'text/plain')
            return file_id
        finally:
            if os.path.exists(temp_file):
                os.remove(temp_file)
    
    def create_review_folder_structure(self, project_name: str) -> Dict[str, str]:
        """Create organized folder structure for code review"""
        timestamp = datetime.now().strftime("%Y-%m-%d")
        
        # Create main Code Reviews folder
        main_folder_id = self.find_or_create_folder("Code Reviews")
        
        # Create date folder
        date_folder_id = self.find_or_create_folder(timestamp, main_folder_id)
        
        # Create project folder
        project_folder_id = self.find_or_create_folder(project_name, date_folder_id)
        
        return {
            'main_folder_id': main_folder_id,
            'date_folder_id': date_folder_id,
            'project_folder_id': project_folder_id,
            'folder_url': f"https://drive.google.com/drive/folders/{project_folder_id}"
        }

class ExcelReportManager:
    """Manages Excel summary reports"""
    
    def __init__(self, drive_manager: GoogleDriveManager):
        self.drive_manager = drive_manager
        self.summary_file_name = "Code_Review_Summary.xlsx"
        self.summary_file_id = None
    
    def find_or_create_summary_file(self) -> str:
        """Find existing summary file or create new one"""
        # Look for existing summary file
        query = f"name='{self.summary_file_name}'"
        results = self.drive_manager.service.files().list(q=query, fields="files(id, name)").execute()
        files = results.get('files', [])
        
        if files:
            self.summary_file_id = files[0]['id']
            return self.summary_file_id
        else:
            # Create new summary file
            return self.create_new_summary_file()
    
    def create_new_summary_file(self) -> str:
        """Create a new Excel summary file with headers"""
        headers = [
            'Project Name', 'Review Date', 'Source Type', 'Source URL',
            'Coding Standards', 'Logic', 'Modularity', 'Database Design',
            'Frontend Design', 'Performance', 'Scalability', 'Security',
            'Usability', 'Total Score', 'Folder Link', 'Review Status'
        ]
        
        df = pd.DataFrame(columns=headers)
        
        # Save to temporary file
        temp_file = f"/tmp/{self.summary_file_name}"
        df.to_excel(temp_file, index=False)
        
        try:
            file_id = self.drive_manager.upload_file(
                temp_file, 
                self.summary_file_name,
                mime_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            self.summary_file_id = file_id
            return file_id
        finally:
            if os.path.exists(temp_file):
                os.remove(temp_file)
    
    def add_review_entry(self, review_data: Dict) -> bool:
        """Add a new review entry to the summary file"""
        try:
            # Download current file
            if not self.summary_file_id:
                self.find_or_create_summary_file()
            
            # Download file content
            request = self.drive_manager.service.files().get_media(fileId=self.summary_file_id)
            file_content = io.BytesIO()
            downloader = MediaIoBaseDownload(file_content, request)
            done = False
            while done is False:
                status, done = downloader.next_chunk()
            
            # Read Excel file
            file_content.seek(0)
            df = pd.read_excel(file_content)
            
            # Add new row
            new_row = pd.DataFrame([review_data])
            df = pd.concat([df, new_row], ignore_index=True)
            
            # Save updated file
            temp_file = f"/tmp/updated_{self.summary_file_name}"
            df.to_excel(temp_file, index=False)
            
            # Upload updated file
            media = MediaFileUpload(
                temp_file,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            
            self.drive_manager.service.files().update(
                fileId=self.summary_file_id,
                media_body=media
            ).execute()
            
            # Clean up
            if os.path.exists(temp_file):
                os.remove(temp_file)
            
            return True
            
        except Exception as e:
            print(f"Error updating Excel summary: {e}")
            return False

def main():
    """Example usage"""
    # Initialize Google Drive manager
    drive_manager = GoogleDriveManager()
    
    # Create folder structure
    folders = drive_manager.create_review_folder_structure("Sample Project")
    print(f"Created folder structure: {folders}")
    
    # Upload a sample report
    sample_report = """
    # Code Review Report
    
    ## Project: Sample Project
    ## Date: 2024-01-15
    
    ### Scores:
    - Coding Standards: 8/10
    - Logic: 7/10
    - Total: 15/20
    """
    
    report_file_id = drive_manager.upload_text_content(
        sample_report, 
        "sample_review.md", 
        folders['project_folder_id']
    )
    print(f"Uploaded report: {report_file_id}")
    
    # Update Excel summary
    excel_manager = ExcelReportManager(drive_manager)
    review_data = {
        'Project Name': 'Sample Project',
        'Review Date': '2024-01-15',
        'Source Type': 'GitHub',
        'Source URL': 'https://github.com/user/repo',
        'Coding Standards': 8,
        'Logic': 7,
        'Modularity': 6,
        'Database Design': 5,
        'Frontend Design': 7,
        'Performance': 6,
        'Scalability': 5,
        'Security': 6,
        'Usability': 7,
        'Total Score': 57,
        'Folder Link': folders['folder_url'],
        'Review Status': 'Completed'
    }
    
    success = excel_manager.add_review_entry(review_data)
    print(f"Excel update success: {success}")

if __name__ == "__main__":
    main()
