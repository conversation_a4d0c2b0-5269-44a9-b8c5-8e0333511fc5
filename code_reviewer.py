#!/usr/bin/env python3
"""
Comprehensive Code Review Engine
Analyzes code repositories and generates detailed review reports
"""

import os
import ast
import json
import re
import subprocess
import tempfile
import shutil
from pathlib import Path
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass
from collections import defaultdict
import requests
import zipfile

@dataclass
class ReviewScore:
    """Data class for review scores"""
    coding_standards: int = 0
    logic: int = 0
    modularity: int = 0
    database_design: int = 0
    frontend_design: int = 0
    performance: int = 0
    scalability: int = 0
    security: int = 0
    usability: int = 0
    
    def total(self) -> int:
        return sum([
            self.coding_standards, self.logic, self.modularity,
            self.database_design, self.frontend_design, self.performance,
            self.scalability, self.security, self.usability
        ])

class CodeReviewer:
    """Main code review engine"""
    
    def __init__(self):
        self.supported_extensions = {
            '.py': 'python',
            '.js': 'javascript',
            '.jsx': 'javascript',
            '.ts': 'typescript',
            '.tsx': 'typescript',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.cs': 'csharp',
            '.php': 'php',
            '.rb': 'ruby',
            '.go': 'go',
            '.rs': 'rust',
            '.html': 'html',
            '.css': 'css',
            '.sql': 'sql',
            '.json': 'json',
            '.xml': 'xml',
            '.yaml': 'yaml',
            '.yml': 'yaml'
        }
        
    def download_github_repo(self, github_url: str, temp_dir: str) -> str:
        """Download GitHub repository as ZIP and extract"""
        try:
            # Convert GitHub URL to download URL
            if 'github.com' in github_url:
                if github_url.endswith('.git'):
                    github_url = github_url[:-4]
                download_url = f"{github_url}/archive/refs/heads/main.zip"
                
                # Try main branch first, then master
                response = requests.get(download_url)
                if response.status_code != 200:
                    download_url = f"{github_url}/archive/refs/heads/master.zip"
                    response = requests.get(download_url)
                
                if response.status_code == 200:
                    zip_path = os.path.join(temp_dir, 'repo.zip')
                    with open(zip_path, 'wb') as f:
                        f.write(response.content)
                    
                    # Extract ZIP
                    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                        zip_ref.extractall(temp_dir)
                    
                    # Find extracted folder
                    for item in os.listdir(temp_dir):
                        item_path = os.path.join(temp_dir, item)
                        if os.path.isdir(item_path) and item != '__pycache__':
                            return item_path
                            
            return None
        except Exception as e:
            print(f"Error downloading GitHub repo: {e}")
            return None
    
    def analyze_file_structure(self, repo_path: str) -> Dict[str, Any]:
        """Analyze repository file structure"""
        structure = {
            'total_files': 0,
            'languages': defaultdict(int),
            'file_sizes': [],
            'directories': [],
            'config_files': [],
            'test_files': [],
            'documentation': []
        }
        
        for root, dirs, files in os.walk(repo_path):
            # Skip hidden directories and common ignore patterns
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['node_modules', '__pycache__', 'venv', 'env']]
            
            structure['directories'].append(root)
            
            for file in files:
                if file.startswith('.'):
                    continue
                    
                file_path = os.path.join(root, file)
                file_ext = Path(file).suffix.lower()
                
                structure['total_files'] += 1
                
                # Get file size
                try:
                    size = os.path.getsize(file_path)
                    structure['file_sizes'].append(size)
                except:
                    pass
                
                # Categorize files
                if file_ext in self.supported_extensions:
                    structure['languages'][self.supported_extensions[file_ext]] += 1
                
                # Identify special files
                if file.lower() in ['package.json', 'requirements.txt', 'pom.xml', 'cargo.toml', 'composer.json']:
                    structure['config_files'].append(file_path)
                elif 'test' in file.lower() or file.startswith('test_'):
                    structure['test_files'].append(file_path)
                elif file.lower() in ['readme.md', 'readme.txt', 'docs.md'] or file_ext in ['.md', '.txt']:
                    structure['documentation'].append(file_path)
        
        return structure
    
    def analyze_python_code(self, file_path: str) -> Dict[str, Any]:
        """Analyze Python code quality"""
        analysis = {
            'functions': 0,
            'classes': 0,
            'lines_of_code': 0,
            'comments': 0,
            'docstrings': 0,
            'complexity_issues': [],
            'style_issues': []
        }
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                analysis['lines_of_code'] = len([line for line in lines if line.strip() and not line.strip().startswith('#')])
                analysis['comments'] = len([line for line in lines if line.strip().startswith('#')])
            
            # Parse AST
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    analysis['functions'] += 1
                    if ast.get_docstring(node):
                        analysis['docstrings'] += 1
                elif isinstance(node, ast.ClassDef):
                    analysis['classes'] += 1
                    if ast.get_docstring(node):
                        analysis['docstrings'] += 1
            
            # Check for style issues
            if 'import *' in content:
                analysis['style_issues'].append('Uses wildcard imports')
            if len([line for line in lines if len(line) > 120]) > 0:
                analysis['style_issues'].append('Lines exceed 120 characters')
                
        except Exception as e:
            analysis['parse_error'] = str(e)
        
        return analysis
