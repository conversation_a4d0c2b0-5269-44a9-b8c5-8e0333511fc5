#!/usr/bin/env python3
"""
Test Script for Code Review Workflow
Tests the complete workflow components
"""

import os
import json
import tempfile
import zipfile
from datetime import datetime
from google_drive_helper import GoogleDriveManager, ExcelReportManager

def test_google_drive_integration():
    """Test Google Drive operations"""
    print("🧪 Testing Google Drive Integration...")
    
    try:
        # Initialize Google Drive manager
        drive_manager = GoogleDriveManager()
        print("✅ Google Drive authentication successful")
        
        # Test folder creation
        folders = drive_manager.create_review_folder_structure("Test Project")
        print(f"✅ Folder structure created: {folders['folder_url']}")
        
        # Test file upload
        test_content = """
# Test Code Review Report

## Project: Test Project
## Date: {}

### Summary:
This is a test report to verify the workflow is working correctly.

### Scores:
- Coding Standards: 8/10
- Logic: 7/10
- Modularity: 6/10
- Database Design: 5/10
- Frontend Design: 7/10
- Performance: 6/10
- Scalability: 5/10
- Security: 6/10
- Usability: 7/10

**Total: 57/90**

### Verdict:
Test completed successfully!
        """.format(datetime.now().strftime('%Y-%m-%d'))
        
        report_id = drive_manager.upload_text_content(
            test_content,
            "test_review_report.md",
            folders['project_folder_id']
        )
        print(f"✅ Test report uploaded: {report_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ Google Drive test failed: {e}")
        return False

def test_excel_operations():
    """Test Excel report management"""
    print("\n🧪 Testing Excel Operations...")
    
    try:
        # Initialize managers
        drive_manager = GoogleDriveManager()
        excel_manager = ExcelReportManager(drive_manager)
        
        # Test Excel file creation/finding
        file_id = excel_manager.find_or_create_summary_file()
        print(f"✅ Excel summary file ready: {file_id}")
        
        # Test adding review entry
        test_review_data = {
            'Project Name': 'Test Project',
            'Review Date': datetime.now().strftime('%Y-%m-%d'),
            'Source Type': 'Test',
            'Source URL': 'https://github.com/test/test-repo',
            'Coding Standards': 8,
            'Logic': 7,
            'Modularity': 6,
            'Database Design': 5,
            'Frontend Design': 7,
            'Performance': 6,
            'Scalability': 5,
            'Security': 6,
            'Usability': 7,
            'Total Score': 57,
            'Folder Link': 'https://drive.google.com/drive/folders/test-folder',
            'Review Status': 'Test Completed'
        }
        
        success = excel_manager.add_review_entry(test_review_data)
        if success:
            print("✅ Excel entry added successfully")
        else:
            print("❌ Excel entry failed")
            
        return success
        
    except Exception as e:
        print(f"❌ Excel test failed: {e}")
        return False

def create_test_github_url_file():
    """Create a test file with GitHub URL"""
    print("\n🧪 Creating Test GitHub URL File...")
    
    try:
        # Create a temporary file with GitHub URL
        test_content = "https://github.com/octocat/Hello-World"
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(test_content)
            temp_file = f.name
        
        print(f"✅ Test GitHub URL file created: {temp_file}")
        print(f"📝 Content: {test_content}")
        print("📋 Upload this file to your 'Code Review Requests' folder in Google Drive")
        
        return temp_file
        
    except Exception as e:
        print(f"❌ Test file creation failed: {e}")
        return None

def create_test_code_zip():
    """Create a test ZIP file with sample code"""
    print("\n🧪 Creating Test Code ZIP File...")
    
    try:
        # Create a temporary ZIP file with sample code
        with tempfile.NamedTemporaryFile(suffix='.zip', delete=False) as temp_zip:
            with zipfile.ZipFile(temp_zip.name, 'w') as zf:
                # Add sample Python file
                sample_py = '''
def hello_world():
    """A simple hello world function"""
    print("Hello, World!")
    return "Hello, World!"

def add_numbers(a, b):
    """Add two numbers together"""
    if not isinstance(a, (int, float)) or not isinstance(b, (int, float)):
        raise TypeError("Both arguments must be numbers")
    return a + b

class Calculator:
    """A simple calculator class"""
    
    def __init__(self):
        self.history = []
    
    def add(self, a, b):
        result = a + b
        self.history.append(f"{a} + {b} = {result}")
        return result
    
    def get_history(self):
        return self.history

if __name__ == "__main__":
    hello_world()
    calc = Calculator()
    print(calc.add(5, 3))
'''
                zf.writestr('sample_code.py', sample_py)
                
                # Add README
                readme_content = '''
# Sample Code Project

This is a test project for the code review workflow.

## Features
- Simple hello world function
- Basic calculator class
- Error handling example

## Usage
```python
from sample_code import Calculator

calc = Calculator()
result = calc.add(5, 3)
print(result)  # Output: 8
```
'''
                zf.writestr('README.md', readme_content)
                
                # Add requirements
                zf.writestr('requirements.txt', 'pytest==7.4.3\nnumpy==1.25.2')
        
        print(f"✅ Test code ZIP created: {temp_zip.name}")
        print("📋 Upload this ZIP file to your 'Code Review Requests' folder in Google Drive")
        
        return temp_zip.name
        
    except Exception as e:
        print(f"❌ Test ZIP creation failed: {e}")
        return None

def validate_n8n_workflow():
    """Validate the n8n workflow JSON"""
    print("\n🧪 Validating n8n Workflow...")
    
    try:
        with open('n8n-code-review-workflow.json', 'r') as f:
            workflow = json.load(f)
        
        # Check required components
        required_keys = ['name', 'nodes', 'connections']
        for key in required_keys:
            if key not in workflow:
                print(f"❌ Missing required key: {key}")
                return False
        
        # Check for required nodes
        node_names = [node.get('name', '') for node in workflow['nodes']]
        required_nodes = [
            'Google Drive Trigger',
            'OpenAI Code Review',
            'OpenAI File Review',
            'Generate Report',
            'Create Folder Structure',
            'Upload Detailed Report',
            'Update Excel Summary'
        ]
        
        missing_nodes = []
        for required_node in required_nodes:
            if required_node not in node_names:
                missing_nodes.append(required_node)
        
        if missing_nodes:
            print(f"❌ Missing required nodes: {missing_nodes}")
            return False
        
        print(f"✅ Workflow validation passed")
        print(f"📊 Total nodes: {len(workflow['nodes'])}")
        print(f"🔗 Total connections: {len(workflow['connections'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ Workflow validation failed: {e}")
        return False

def run_all_tests():
    """Run all tests"""
    print("🚀 Starting Code Review Workflow Tests")
    print("=" * 50)
    
    test_results = []
    
    # Test 1: Validate n8n workflow
    test_results.append(validate_n8n_workflow())
    
    # Test 2: Google Drive integration (requires credentials)
    if os.path.exists('credentials.json'):
        test_results.append(test_google_drive_integration())
        test_results.append(test_excel_operations())
    else:
        print("\n⚠️  Skipping Google Drive tests - credentials.json not found")
        print("   Please set up Google Drive API credentials to run these tests")
    
    # Test 3: Create test files
    github_file = create_test_github_url_file()
    zip_file = create_test_code_zip()
    
    if github_file and zip_file:
        test_results.append(True)
    else:
        test_results.append(False)
    
    # Summary
    print("\n" + "=" * 50)
    print("🏁 Test Summary")
    print("=" * 50)
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"✅ Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! Your workflow is ready to use.")
        print("\n📋 Next Steps:")
        print("1. Import the n8n workflow JSON into your n8n instance")
        print("2. Configure OpenAI and Google Drive credentials in n8n")
        print("3. Upload the test files to your Google Drive 'Code Review Requests' folder")
        print("4. Monitor the workflow execution in n8n")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
    
    # Cleanup
    if github_file and os.path.exists(github_file):
        print(f"\n🧹 Cleaning up: {github_file}")
        # os.unlink(github_file)  # Uncomment to auto-delete
    
    if zip_file and os.path.exists(zip_file):
        print(f"🧹 Cleaning up: {zip_file}")
        # os.unlink(zip_file)  # Uncomment to auto-delete

if __name__ == "__main__":
    run_all_tests()
