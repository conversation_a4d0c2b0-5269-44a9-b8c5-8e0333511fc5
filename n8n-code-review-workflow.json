{"name": "Code Review Automation Workflow", "nodes": [{"parameters": {}, "id": "start-node", "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"triggerOn": "fileCreated", "watchFolder": "Code Review Requests", "options": {}}, "id": "google-drive-trigger", "name": "Google Drive Trigger", "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [460, 300], "credentials": {"googleDriveOAuth2Api": {"id": "google-drive-credentials", "name": "Google Drive Account"}}}, {"parameters": {"operation": "download", "fileId": "={{ $json.id }}"}, "id": "download-file", "name": "Download Input File", "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [680, 300], "credentials": {"googleDriveOAuth2Api": {"id": "google-drive-credentials", "name": "Google Drive Account"}}}, {"parameters": {"jsCode": "// Extract GitHub URL or process uploaded code files\nconst inputData = $input.all();\nconst results = [];\n\nfor (const item of inputData) {\n  const fileName = item.json.name || 'unknown';\n  const fileContent = item.binary?.data?.data || '';\n  \n  let processType = 'file';\n  let sourceUrl = '';\n  let codeContent = '';\n  \n  // Check if it's a text file with GitHub URL\n  if (fileName.endsWith('.txt') && fileContent) {\n    const content = Buffer.from(fileContent, 'base64').toString('utf-8');\n    const githubUrlMatch = content.match(/https:\\/\\/github\\.com\\/[\\w\\-\\.]+\\/[\\w\\-\\.]+/g);\n    \n    if (githubUrlMatch) {\n      processType = 'github';\n      sourceUrl = githubUrlMatch[0];\n    }\n  } else if (fileName.endsWith('.zip')) {\n    processType = 'zip';\n    codeContent = fileContent;\n  }\n  \n  results.push({\n    json: {\n      fileName,\n      processType,\n      sourceUrl,\n      codeContent,\n      originalFileId: item.json.id\n    },\n    binary: item.binary\n  });\n}\n\nreturn results;"}, "id": "process-input", "name": "Process Input", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "github-condition", "leftValue": "={{ $json.processType }}", "rightValue": "github", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "check-input-type", "name": "Check Input Type", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"method": "GET", "url": "={{ $json.sourceUrl.replace('github.com', 'api.github.com/repos') }}/contents", "options": {"response": {"response": {"fullResponse": false, "neverError": false, "responseFormat": "json"}}}}, "id": "fetch-github-repo", "name": "Fetch GitHub Repository", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1340, 200]}, {"parameters": {"jsCode": "// Process GitHub repository structure and extract code files\nconst items = $input.all();\nconst results = [];\n\nfor (const item of items) {\n  const repoContents = Array.isArray(item.json) ? item.json : [item.json];\n  let codeFiles = [];\n  let projectStructure = {\n    totalFiles: 0,\n    languages: {},\n    directories: [],\n    mainFiles: []\n  };\n  \n  // Extract relevant code files\n  for (const file of repoContents) {\n    if (file.type === 'file') {\n      const extension = file.name.split('.').pop()?.toLowerCase();\n      const codeExtensions = ['py', 'js', 'jsx', 'ts', 'tsx', 'java', 'cpp', 'c', 'cs', 'php', 'rb', 'go', 'rs'];\n      \n      if (codeExtensions.includes(extension)) {\n        codeFiles.push({\n          name: file.name,\n          path: file.path,\n          downloadUrl: file.download_url,\n          size: file.size,\n          language: extension\n        });\n        \n        projectStructure.languages[extension] = (projectStructure.languages[extension] || 0) + 1;\n        projectStructure.totalFiles++;\n      }\n      \n      // Identify main files\n      if (['readme.md', 'package.json', 'requirements.txt', 'main.py', 'index.js', 'app.py'].includes(file.name.toLowerCase())) {\n        projectStructure.mainFiles.push(file.name);\n      }\n    } else if (file.type === 'dir') {\n      projectStructure.directories.push(file.name);\n    }\n  }\n  \n  results.push({\n    json: {\n      ...item.json,\n      codeFiles: codeFiles.slice(0, 20), // Limit to first 20 files\n      projectStructure,\n      repoUrl: $('Check Input Type').item.json.sourceUrl\n    }\n  });\n}\n\nreturn results;"}, "id": "process-github-repo", "name": "Process GitHub Repository", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1560, 200]}, {"parameters": {"resource": "chat", "operation": "create", "chatId": "gpt-4", "text": "=You are an expert code reviewer. Please analyze the following code repository and provide a comprehensive review.\n\n**Repository Information:**\n- URL: {{ $json.repoUrl }}\n- Total Files: {{ $json.projectStructure.totalFiles }}\n- Languages: {{ Object.keys($json.projectStructure.languages).join(', ') }}\n- Main Files: {{ $json.projectStructure.mainFiles.join(', ') }}\n- Directories: {{ $json.projectStructure.directories.slice(0, 10).join(', ') }}\n\n**Code Files to Review:**\n{{ $json.codeFiles.map(f => `- ${f.name} (${f.language}, ${f.size} bytes)`).join('\\n') }}\n\nPlease provide a detailed review covering these 9 aspects with scores out of 10:\n\n1. **Coding Standards** - Code formatting, naming conventions, documentation\n2. **Logic** - Algorithm efficiency, control flow, error handling\n3. **Modularity** - Code organization, reusability, separation of concerns\n4. **Database Design** - Schema design, relationships, indexing (if applicable)\n5. **Frontend Design** - UI/UX, accessibility, responsive design (if applicable)\n6. **Performance** - Optimization, caching, resource usage\n7. **Scalability** - Architecture for growth, load handling\n8. **Security** - Input validation, authentication, data protection\n9. **Usability** - User experience, error messages, documentation\n\nFormat your response as:\n\n✅ Review of \"[Project Name]\"\n\n---\n\n1️⃣ Coding Standards\nScore (out of 10): [X]\nDetailed Feedback:\nStrengths:\n- [Point 1]\n- [Point 2]\n\nWeaknesses:\n- [Point 1]\n- [Point 2]\n\n[Continue for all 9 aspects]\n\n---\n\n📌 Summary Table\n| Attribute | Score (out of 10) |\n|-----------|------------------|\n| Coding Standards | X |\n| Logic | X |\n| Modularity | X |\n| Database Design | X |\n| Frontend Design | X |\n| Performance | X |\n| Scalability | X |\n| Security | X |\n| Usability | X |\n\n---\n\n✅ Total: [XX] / 90\n\n[Brief overall assessment]\n\n✅ Verdict: [Assessment of competitiveness]", "options": {"temperature": 0.3, "maxTokens": 2000}}, "id": "openai-code-review", "name": "OpenAI Code Review", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [1780, 200], "credentials": {"openAiApi": {"id": "openai-credentials", "name": "OpenAI Account"}}}, {"parameters": {"jsCode": "// Process direct file upload (ZIP or individual files)\nconst items = $input.all();\nconst results = [];\n\nfor (const item of items) {\n  // For direct file uploads, create a simple structure\n  const fileName = item.json.fileName || 'uploaded-code';\n  const projectName = fileName.replace(/\\.(zip|tar|gz)$/, '');\n  \n  results.push({\n    json: {\n      ...item.json,\n      projectName,\n      codeFiles: [{\n        name: fileName,\n        type: 'uploaded',\n        size: 'unknown'\n      }],\n      projectStructure: {\n        totalFiles: 1,\n        languages: { 'mixed': 1 },\n        directories: [],\n        mainFiles: [fileName]\n      }\n    },\n    binary: item.binary\n  });\n}\n\nreturn results;"}, "id": "process-direct-upload", "name": "Process Direct Upload", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 400]}, {"parameters": {"resource": "chat", "operation": "create", "chatId": "gpt-4", "text": "=You are an expert code reviewer. Please analyze the following uploaded code file and provide a comprehensive review.\n\n**File Information:**\n- Name: {{ $json.fileName }}\n- Type: {{ $json.projectStructure.languages | keys | first }}\n\nPlease provide a detailed review covering these 9 aspects with scores out of 10:\n\n1. **Coding Standards** - Code formatting, naming conventions, documentation\n2. **Logic** - Algorithm efficiency, control flow, error handling\n3. **Modularity** - Code organization, reusability, separation of concerns\n4. **Database Design** - Schema design, relationships, indexing (if applicable)\n5. **Frontend Design** - UI/UX, accessibility, responsive design (if applicable)\n6. **Performance** - Optimization, caching, resource usage\n7. **Scalability** - Architecture for growth, load handling\n8. **Security** - Input validation, authentication, data protection\n9. **Usability** - User experience, error messages, documentation\n\nFormat your response exactly like the GitHub repository review format with scores, strengths, weaknesses, and summary table.", "options": {"temperature": 0.3, "maxTokens": 2000}}, "id": "openai-file-review", "name": "OpenAI File Review", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [1560, 400], "credentials": {"openAiApi": {"id": "openai-credentials", "name": "OpenAI Account"}}}, {"parameters": {"jsCode": "// Generate formatted report with timestamp and metadata\nconst items = $input.all();\nconst results = [];\n\nfor (const item of items) {\n  const timestamp = new Date().toISOString().split('T')[0];\n  const projectName = item.json.projectName || item.json.repoUrl?.split('/').pop() || 'Unknown Project';\n  const reviewContent = item.json.choices?.[0]?.message?.content || item.json.text || 'Review content not available';\n  \n  // Extract scores from the review content\n  const scoreMatches = reviewContent.match(/Score \\(out of 10\\): (\\d+)/g) || [];\n  const scores = scoreMatches.map(match => parseInt(match.match(/\\d+/)[0]));\n  const totalScore = scores.reduce((sum, score) => sum + score, 0);\n  \n  const report = {\n    projectName,\n    timestamp,\n    reviewContent,\n    scores: {\n      codingStandards: scores[0] || 0,\n      logic: scores[1] || 0,\n      modularity: scores[2] || 0,\n      databaseDesign: scores[3] || 0,\n      frontendDesign: scores[4] || 0,\n      performance: scores[5] || 0,\n      scalability: scores[6] || 0,\n      security: scores[7] || 0,\n      usability: scores[8] || 0,\n      total: totalScore\n    },\n    sourceType: item.json.repoUrl ? 'GitHub' : 'Upload',\n    sourceUrl: item.json.repoUrl || 'Direct Upload'\n  };\n  \n  results.push({\n    json: report\n  });\n}\n\nreturn results;"}, "id": "generate-report", "name": "Generate Report", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2000, 300]}, {"parameters": {"operation": "create", "name": "=Code Reviews/{{ $json.timestamp }}/{{ $json.projectName }}", "options": {}}, "id": "create-folder-structure", "name": "Create Folder Structure", "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [2220, 300], "credentials": {"googleDriveOAuth2Api": {"id": "google-drive-credentials", "name": "Google Drive Account"}}}, {"parameters": {"operation": "upload", "name": "={{ $json.projectName }}_detailed_review.md", "parents": {"parentId": "={{ $('Create Folder Structure').item.json.id }}"}, "options": {"uploadData": "={{ $('Generate Report').item.json.reviewContent }}"}}, "id": "upload-detailed-report", "name": "Upload Detailed Report", "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [2440, 300], "credentials": {"googleDriveOAuth2Api": {"id": "google-drive-credentials", "name": "Google Drive Account"}}}, {"parameters": {"operation": "update", "fileId": "EXCEL_SUMMARY_FILE_ID", "options": {"uploadData": "=Project Name,Review Date,Source Type,Source URL,Coding Standards,Logic,Modularity,Database Design,Frontend Design,Performance,Scalability,Security,Usability,Total Score,Folder Link\\n{{ $('Generate Report').item.json.projectName }},{{ $('Generate Report').item.json.timestamp }},{{ $('Generate Report').item.json.sourceType }},{{ $('Generate Report').item.json.sourceUrl }},{{ $('Generate Report').item.json.scores.codingStandards }},{{ $('Generate Report').item.json.scores.logic }},{{ $('Generate Report').item.json.scores.modularity }},{{ $('Generate Report').item.json.scores.databaseDesign }},{{ $('Generate Report').item.json.scores.frontendDesign }},{{ $('Generate Report').item.json.scores.performance }},{{ $('Generate Report').item.json.scores.scalability }},{{ $('Generate Report').item.json.scores.security }},{{ $('Generate Report').item.json.scores.usability }},{{ $('Generate Report').item.json.scores.total }},https://drive.google.com/drive/folders/{{ $('Create Folder Structure').item.json.id }}"}}, "id": "update-excel-summary", "name": "Update Excel Summary", "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [2660, 300], "credentials": {"googleDriveOAuth2Api": {"id": "google-drive-credentials", "name": "Google Drive Account"}}}], "connections": {"Start": {"main": [[{"node": "Google Drive Trigger", "type": "main", "index": 0}]]}, "Google Drive Trigger": {"main": [[{"node": "Download Input File", "type": "main", "index": 0}]]}, "Download Input File": {"main": [[{"node": "Process Input", "type": "main", "index": 0}]]}, "Process Input": {"main": [[{"node": "Check Input Type", "type": "main", "index": 0}]]}, "Check Input Type": {"main": [[{"node": "Fetch GitHub Repository", "type": "main", "index": 0}], [{"node": "Process Direct Upload", "type": "main", "index": 0}]]}, "Fetch GitHub Repository": {"main": [[{"node": "Process GitHub Repository", "type": "main", "index": 0}]]}, "Process GitHub Repository": {"main": [[{"node": "OpenAI Code Review", "type": "main", "index": 0}]]}, "Process Direct Upload": {"main": [[{"node": "OpenAI File Review", "type": "main", "index": 0}]]}, "OpenAI Code Review": {"main": [[{"node": "Generate Report", "type": "main", "index": 0}]]}, "OpenAI File Review": {"main": [[{"node": "Generate Report", "type": "main", "index": 0}]]}, "Generate Report": {"main": [[{"node": "Create Folder Structure", "type": "main", "index": 0}]]}, "Create Folder Structure": {"main": [[{"node": "Upload Detailed Report", "type": "main", "index": 0}]]}, "Upload Detailed Report": {"main": [[{"node": "Update Excel Summary", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 1, "updatedAt": "2024-01-15T10:00:00.000Z", "versionId": "1"}